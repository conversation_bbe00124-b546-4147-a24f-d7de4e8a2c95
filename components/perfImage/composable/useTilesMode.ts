import type { Ref } from "vue"
import { computed, ref, watch } from "vue"
import { Mark, ImageConfig, ScreenMode, MarkType, Tile } from "../type"
import Konva from "konva"
import { drawConfig } from "../config"
import { chunk, throttle, debounce } from "lodash"
import {
  calculateGrayValue,
  getViewportBounds,
  isMarkInViewport,
  setDrawMode,
} from "../tools"
import { useTileCache } from "./useTileCache"
import { ElMessage } from "element-plus"
export const useTilesMode = (
	props: {
		// imageConfig: ImageConfig;
		initialMarks: Mark[];
		isTile: boolean;
	},
	screenMode: Ref<ScreenMode>,
	container: Ref<HTMLElement | null>,
	isReady: Ref<boolean>,
	tilesUrl: Ref<string>,
	imageConfig: Ref<ImageConfig>
) => {
	const { tileCache, cleanupTileCache } = useTileCache();

	const stageContainer = ref<HTMLElement | null>(null); // 舞台容器
	const stage = ref<Konva.Stage | null>(null); // 舞台
	const layer = ref<Konva.Layer | null>(null); // 瓦片层
	const drawLayer = ref<Konva.Layer | null>(null); // 绘制层
	const simplifiedMode = ref(false); // 简化模式，优化渲染
	const lowResImage = ref<HTMLImageElement | null>(null); // 低分辨率图片 （2025-07-24 新增最小化逻辑）
	const isLowResImageLoading = ref(false); // 低分辨率图片是否正在加载 （2025-07-24 新增最小化逻辑）
	const fullImage = ref<HTMLImageElement | null>(null); // 不切图模式的完整图片
	const isFullImageLoading = ref(false); // 不切图模式的图片是否正在加载

	const scale = ref(1); // 缩放
	const position = ref({ x: 0, y: 0 }); // 位置
	const dragStartPosition = ref({ x: 0, y: 0 }); // 拖动开始位置
	const dragLastPosition = ref({ x: 0, y: 0 }); // 拖动结束位置
	const isDragging = ref(false); // 是否拖动

	const copiedMarks = ref<Mark[]>([]); // 复制标记数据
	const isCopyMode = ref(false); // 是否复制模式

	const isDrawMode = ref(false); // 是否绘制模式
	const marks = ref<Mark[]>(props.initialMarks || []); // 标记数据
	const drawShapeType = ref<MarkType>("rect"); // 绘制形状类型
	const currentPolygon = ref<Konva.Line | null>(null); // 当前多边形
	const polygonPoints = ref<number[]>([]); // 多边形点
	const markStartPosition = ref<{ x: number; y: number; id: string }>({
		x: 0,
		y: 0,
		id: "",
	}); // 标记开始位置

	const showLength = ref(false); // 长度指示器
	const currentLength = ref(0); // 当前长度
	const lengthIndicatorStyle = ref({
		left: "0px",
		top: "0px",
	});

	const showCoordinates = ref(false); // 坐标指示器
	const coordinates = ref({ x: 0, y: 0 }); // 坐标
	const grayValue = ref(0); // 灰度值

	// 计算当前视口下需要的最小缩放比
	const minScale = computed(() => {
		if (!container.value) return 1;

		// 如果是切图模式，最小缩放比为1
		if (props.isTile) return 1;

		// 不切图模式，如果图片已加载，使用实际尺寸计算
		if (fullImage.value) {
			const containerWidth = container.value.clientWidth;
			const containerHeight = container.value.clientHeight;
			const imageWidth = fullImage.value.naturalWidth;
			const imageHeight = fullImage.value.naturalHeight;

			// 计算图片适应视口所需的缩放比
			const scaleX = containerWidth / imageWidth;
			const scaleY = containerHeight / imageHeight;

			// 使用较小的缩放比，确保图片可以完全显示在视口中
			const fitScale = Math.min(scaleX, scaleY) * 0.95; // 乘以0.95确保有一点边距
			return Math.max(fitScale, 0.1); // 设置最小不低于0.1
		}

		// 图片未加载时返回默认值
		return 0.25;
	});

	// 计算最大缩放比例
	const maxScale = computed(() => {
		// 切图模式使用imageConfig中的最大缩放级别
		if (props.isTile) {
			return imageConfig.value.maxZoom;
		}
		// 不切图模式最大缩放比为1
		return 1;
	});

	// 瓦片模式工具栏相关
	const currentTileTool = ref<string | null>(null);
	const tileTools = ref([
		{ type: "circle", icon: "icon-big-circle", label: "圆形" },
		{ type: "rect", icon: "icon-juxing", label: "矩形" },
		{ type: "polygon", icon: "icon-polygon", label: "多边形" },
		{ type: "line", icon: "icon-chizi", label: "直线" },
		{ type: "copy", icon: "icon-copy1", label: "复制" },
		{ type: "clear", icon: "icon-qingchu", label: "清除" },
	]);

	// 加载不切图模式的完整图片
	const loadFullImage = () => {
		if (isFullImageLoading.value || fullImage.value) return;

		isFullImageLoading.value = true;

		const img = new Image();
		img.crossOrigin = "anonymous";
		img.onload = () => {
			fullImage.value = img;
			isFullImageLoading.value = false;

			// 图片加载后重新计算缩放比例和位置
			if (!props.isTile && container.value && stage.value) {
				// 获取图片实际尺寸
				const actualWidth = img.naturalWidth;
				const actualHeight = img.naturalHeight;

				// 获取容器尺寸
				const containerWidth = container.value.clientWidth;
				const containerHeight = container.value.clientHeight;

				// 计算适合的缩放比例，确保图片完全展示
				const scaleX = containerWidth / actualWidth;
				const scaleY = containerHeight / actualHeight;
				const fitScale = Math.min(scaleX, scaleY) * 0.95; // 稍微缩小一点，确保完全可见
				const newScale = Math.max(fitScale, 0.1); // 最小不低于0.1

				// 更新缩放
				scale.value = newScale;

				// 计算居中位置
				const newPosition = {
					x: (containerWidth - actualWidth * newScale) / 2,
					y: (containerHeight - actualHeight * newScale) / 2,
				};
				position.value = newPosition;

				// 更新舞台
				stage.value.scale({ x: newScale, y: newScale });
				stage.value.position(newPosition);
				stage.value.batchDraw();

				// 渲染图片
				renderFullImage();
			} else if (layer.value) {
				// 切图模式直接渲染
				renderFullImage();
			}
		};
		img.onerror = (error) => {
			isFullImageLoading.value = false;
		};

		// 加载完整图片
		img.src = tilesUrl.value;
	};

	// 渲染不切图模式的完整图片
	const renderFullImage = () => {
		if (!layer.value || !fullImage.value) return;

		// 清除所有瓦片
		layer.value.destroyChildren();

		// 创建图片对象，使用图片的实际尺寸
		const konvaImage = new Konva.Image({
			x: 0,
			y: 0,
			image: fullImage.value,
			width: props.isTile
				? imageConfig.value.width
				: fullImage.value.naturalWidth,
			height: props.isTile
				? imageConfig.value.height
				: fullImage.value.naturalHeight,
			id: "full-image",
		});

		layer.value.add(konvaImage);
		layer.value.batchDraw();
	};

	// 加载低分辨率图片 （2025-07-24 新增最小化逻辑）
	const loadLowResImage = () => {
		if (isLowResImageLoading.value || lowResImage.value) return;

		// 如果是不切图模式，则使用完整图片作为低分辨率图片
		if (!props.isTile) {
			if (fullImage.value) {
				lowResImage.value = fullImage.value;
				if (scale.value < 1 && layer.value) {
					renderLowResImage();
				}
			} else {
				loadFullImage();
			}
			return;
		}

		isLowResImageLoading.value = true;

		// 使用第一级别的图片作为低分辨率图片
		const img = new Image();
		img.crossOrigin = "anonymous";
		img.onload = () => {
			lowResImage.value = img;
			isLowResImageLoading.value = false;

			// 如果当前缩放比例小于1，需要重新渲染
			if (scale.value < 1 && layer.value) {
				renderLowResImage();
			}
		};
		img.onerror = () => {
			console.error("低分辨率图片加载失败");
			isLowResImageLoading.value = false;
		};

		// 加载第一级别的中心瓦片
		img.src = `${tilesUrl.value}/1/0/0.png`;
	};

	// 渲染低分辨率图片 （2025-07-24 新增最小化逻辑）
	const renderLowResImage = () => {
		if (!layer.value || !lowResImage.value) return;

		// 清除所有瓦片
		layer.value.destroyChildren();

		// 创建图片对象
		const konvaImage = new Konva.Image({
			x: 0,
			y: 0,
			image: lowResImage.value,
			width: props.isTile
				? imageConfig.value.width / imageConfig.value.factor
				: lowResImage.value.naturalWidth,
			height: props.isTile
				? imageConfig.value.height / imageConfig.value.factor
				: lowResImage.value.naturalHeight,
			id: "low-res-image",
		});

		layer.value.add(konvaImage);
		layer.value.batchDraw();
	};

	// 处理缩放变化，决定是显示低分辨率图片还是瓦片
	watch(scale, (newScale, oldScale) => {
		// 不切图模式下，总是使用完整图片，不需要切换显示方式
		if (!props.isTile) {
			return;
		}

		// 从>=1变为<1，显示低分辨率图片
		if (newScale < 1 && oldScale >= 1) {
			if (lowResImage.value) {
				renderLowResImage();
			} else {
				loadLowResImage();
			}
		}
		// 从<1变为>=1，重新加载瓦片
		else if (newScale >= 1 && oldScale < 1) {
			// visibleTiles的计算会自动触发瓦片加载
			if (layer.value) {
				const lowResImageNode = layer.value.findOne("#low-res-image");
				if (lowResImageNode) {
					lowResImageNode.destroy();
				}
				layer.value.batchDraw();
			}
		}
	});

	const handleTileToolClick = (tool: { type: string; icon: string }) => {
		if (!drawLayer.value) return;

		resetMarkSelected();

		if (tool.type === "clear") {
			// 清除所有标记
			marks.value = [];
			if (drawLayer.value) {
				drawLayer.value.destroyChildren();
				drawLayer.value.batchDraw();
			}
			currentTileTool.value = null;
			isDrawMode.value = false;
			isCopyMode.value = false;
			copiedMarks.value = [];
			return;
		}

		currentTileTool.value = tool.type;
		if (
			tool.type === "rect" ||
			tool.type === "circle" ||
			tool.type === "polygon" ||
			tool.type === "line"
		) {
			setDrawMode(
				tool.type as MarkType,
				drawShapeType,
				isDrawMode,
				polygonPoints
			);
			isCopyMode.value = false;
			copiedMarks.value = [];
		} else if (tool.type === "copy") {
			if (copiedMarks.value.length > 0) {
				isCopyMode.value = true;
			} else {
				ElMessage.warning("请选中需要复制的标记");
				currentTileTool.value = null;
			}
		}
	};

	// 优化visibleTiles计算
	const visibleTiles = computed(() => {
		const currentTilesUrl = tilesUrl.value;

		// 如果是不切图模式，不计算瓦片，直接返回空数组
		if (!props.isTile) return [];

		// 当缩放比例小于1时，不计算瓦片，直接返回空数组 （2025-07-24 修改最小化逻辑）
		if (
			scale.value < 1 ||
			!container.value ||
			screenMode.value !== "tiles" ||
			!isReady.value
		)
			return [];

		const containerRect = container.value.getBoundingClientRect();
		const currentZoom = Math.floor(scale.value);

		// 计算当前缩放级别下的实际图片尺寸
		const currentLevelScale =
			1 /
			Math.pow(
				imageConfig.value.factor,
				imageConfig.value.maxZoom - currentZoom
			);
		const currentWidth = imageConfig.value.width * currentLevelScale;
		const currentHeight = imageConfig.value.height * currentLevelScale;

		// 计算视口范围内的瓦片索引
		const viewportLeft = -position.value.x / scale.value;
		const viewportTop = -position.value.y / scale.value;
		const viewportWidth = containerRect.width / scale.value;
		const viewportHeight = containerRect.height / scale.value;

		// 计算瓦片范围
		const startCol = Math.floor(viewportLeft / imageConfig.value.tileSize);
		const startRow = Math.floor(viewportTop / imageConfig.value.tileSize);
		const endCol = Math.ceil(
			(viewportLeft + viewportWidth) / imageConfig.value.tileSize
		);
		const endRow = Math.ceil(
			(viewportTop + viewportHeight) / imageConfig.value.tileSize
		);

		// 最大瓦片数计算
		const maxCol = Math.ceil(currentWidth / imageConfig.value.tileSize);
		const maxRow = Math.ceil(currentHeight / imageConfig.value.tileSize);

		const tiles: Tile[] = [];

		// 计算视口中心位置
		const centerCol = Math.floor((startCol + endCol) / 2);
		const centerRow = Math.floor((startRow + endRow) / 2);

		// 按照从中心向外的顺序生成瓦片，优先加载中心区域
		const coordinates: Array<[number, number]> = [];

		// 计算视口内所有瓦片坐标
		for (
			let row = Math.max(0, startRow);
			row < Math.min(endRow, maxRow);
			row++
		) {
			for (
				let col = Math.max(0, startCol);
				col < Math.min(endCol, maxCol);
				col++
			) {
				coordinates.push([row, col]);
			}
		}

		// 按照与中心的距离排序瓦片
		coordinates.sort((a, b) => {
			const distA =
				Math.pow(a[0] - centerRow, 2) + Math.pow(a[1] - centerCol, 2);
			const distB =
				Math.pow(b[0] - centerRow, 2) + Math.pow(b[1] - centerCol, 2);
			return distA - distB;
		});

		// 根据排序后的坐标生成瓦片
		for (const [row, col] of coordinates) {
			const tileId = `${currentTilesUrl}-${currentZoom}-${col}-${row}`;

			let tile = tileCache.get(tileId); // 检查缓存

			if (!tile) {
				tile = {
					id: tileId,
					x: col,
					y: row,
					z: currentZoom,
					url: `${currentTilesUrl}/${currentZoom}/${col}/${row}.png`,
				};
				tileCache.set(tileId, tile);
			}

			tiles.push(tile);
		}

		cleanupTileCache(tiles); // 清理可见的瓦片缓存

		return tiles;
	});

	// 更新渲染 tiles - 优化与分批加载
	watch(
		visibleTiles,
		(newTiles) => {
			if (!layer.value) return;

			// 如果是不切图模式，只需要渲染完整图片即可
			if (!props.isTile) {
				if (fullImage.value) {
					renderFullImage();
				} else {
					loadFullImage();
				}
				return;
			}

			// 如果缩放比例小于1，显示低分辨率图片 （2025-07-24 新增最小化逻辑）
			if (scale.value < 1) {
				if (lowResImage.value) {
					renderLowResImage();
				} else {
					loadLowResImage();
				}
				return;
			}

			const existingTiles = new Set(
				layer.value.children?.map((child) => child.attrs.id)
			);

			// 移除不再可见的瓦片
			existingTiles.forEach((tileId) => {
				if (!newTiles.some((tile) => tile.id === tileId)) {
					const node = layer.value?.findOne(`#${tileId}`);
					node?.destroy();
				}
			});

			// 准备批量加载
			const MAX_CONCURRENT_LOADS = 8; // 最大并发加载数
			let loadingCount = 0;
			const loadQueue = [...newTiles];

			// 分批加载函数
			const loadNextBatch = () => {
				// 如果已加载完所有瓦片或组件已销毁，则停止
				if (loadQueue.length === 0 || !layer.value) return;

				// 加载下一批瓦片直到达到并发上限
				while (
					loadingCount < MAX_CONCURRENT_LOADS &&
					loadQueue.length > 0
				) {
					const tile = loadQueue.shift();
					if (!tile) continue;

					// 如果瓦片已存在，跳过
					if (existingTiles.has(tile.id)) {
						existingTiles.delete(tile.id);
						continue;
					}

					loadingCount++;

					const image = new Image();
					image.crossOrigin = "anonymous";
					image.src = tile.url;

					image.onload = () => {
						loadingCount--;

						// 确保layer仍然存在
						if (!layer.value) return;

						// 使用图片原始尺寸或tileSize中较小的值，避免拉伸
						const imageWidth = Math.min(
							image.naturalWidth,
							imageConfig.value.tileSize
						);
						const imageHeight = Math.min(
							image.naturalHeight,
							imageConfig.value.tileSize
						);

						const konvaImage = new Konva.Image({
							x: tile.x * imageConfig.value.tileSize,
							y: tile.y * imageConfig.value.tileSize,
							image: image,
							width: imageWidth,
							height: imageHeight,
							id: tile.id,
						});

						layer.value.add(konvaImage);

						// 继续加载下一批
						loadNextBatch();
					};

					image.onerror = () => {
						// 加载失败也减少计数
						loadingCount--;
						loadNextBatch();
					};
				}
			};

			// 开始加载
			loadNextBatch();

			// 使用请求动画帧优化绘制
			requestAnimationFrame(() => {
				layer.value?.batchDraw();
			});
		},
		{ deep: true }
	);

	// 瓦片模式初始化
	const initStage = () => {
		if (!stageContainer.value) return;

		// 清理旧的 stage 和事件
		if (stage.value) {
			stage.value.off("mousemove touchmove");
			stage.value.off("click");
			stage.value.destroy();
		}

		// 重置位置和缩放
		position.value = { x: 0, y: 0 };

		// 设置初始缩放值：
		// - 切图模式使用1作为初始值
		// - 不切图模式使用minScale作为初始值(保证图片完整显示)
		scale.value = props.isTile ? 1 : minScale.value;

		// 获取容器尺寸
		const containerWidth = stageContainer.value.clientWidth;
		const containerHeight = stageContainer.value.clientHeight;

		stage.value = new Konva.Stage({
			container: stageContainer.value as HTMLDivElement,
			width: containerWidth,
			height: containerHeight,
		});

		layer.value = new Konva.Layer();
		drawLayer.value = new Konva.Layer();

		stage.value.add(layer.value as Konva.Layer);
		stage.value.add(drawLayer.value as Konva.Layer);

		// 计算初始位置
		// 切图模式使用配置的尺寸
		if (props.isTile) {
			const currentLevelScale =
				1 /
				Math.pow(
					imageConfig.value.factor,
					imageConfig.value.maxZoom - 1
				);
			const initialWidth = imageConfig.value.width * currentLevelScale;
			const initialHeight = imageConfig.value.height * currentLevelScale;

			position.value = {
				x: (containerWidth - initialWidth * scale.value) / 2,
				y: (containerHeight - initialHeight * scale.value) / 2,
			};
		} else {
			// 不切图模式初始位置居中
			position.value = {
				x: containerWidth / 2,
				y: containerHeight / 2,
			};
		}

		// 设置舞台位置和缩放
		stage.value.position(position.value);
		stage.value.scale({ x: scale.value, y: scale.value });

		// 设置舞台监听器
		stage.value.on("mousemove touchmove", (e) => {
			if (e.evt) {
				stage.value?.setPointersPositions(e.evt);
			}

			// 添加坐标显示逻辑
			const pos = stage.value?.getPointerPosition();
			if (pos) {
				const imagePos = {
					x: Math.round((pos.x - position.value.x) / scale.value),
					y: Math.round((pos.y - position.value.y) / scale.value),
				};

				// 更新坐标显示
				coordinates.value = imagePos;
				showCoordinates.value = true;

				// 计算灰度值
				if (props.isTile) {
					// 切图模式使用原有的灰度值计算方法
					calculateGrayValue(
						imagePos,
						stage,
						layer,
						scale,
						imageConfig.value,
						grayValue
					);
				} else if (fullImage.value) {
					// 不切图模式下，从完整图片中计算灰度值
					calculateGrayValueFromFullImage(
						imagePos,
						fullImage.value,
						grayValue
					);
				} else {
					grayValue.value = -1;
				}
			}
		});

		// 添加鼠标离开舞台时隐藏坐标指示器
		stageContainer.value.addEventListener("mouseleave", () => {
			showCoordinates.value = false;
		});

		// 初始化时渲染已有标记
		if (marks.value.length > 0) {
			renderMarks();
		}

		// 如果是不切图模式，直接加载完整图片
		if (!props.isTile) {
			// 清空之前可能存在的图片引用
			fullImage.value = null;
			loadFullImage();
		}
		// 如果是切图模式且初始缩放比例小于1，加载低分辨率图片
		else if (scale.value < 1) {
			loadLowResImage();
		}

		// 触发一次重绘
		stage.value.batchDraw();
	};

	// 处理标记绘制开始
	const handleShapeDraw = (
		imagePos: { x: number; y: number },
		type: MarkType
	) => {
		if (!drawLayer.value) return;

		const id = Date.now().toString();
		markStartPosition.value = { ...imagePos, id };

		let shape: Konva.Shape | null = null;
		switch (type) {
			case "rect":
				shape = new Konva.Rect({
					x: imagePos.x,
					y: imagePos.y,
					width: 0,
					height: 0,
					fill: drawConfig.fill,
					stroke: drawConfig.stroke,
					strokeWidth: drawConfig.strokeWidth,
					name: "mark",
					id: id,
				});
				break;
			case "circle":
				shape = new Konva.Circle({
					x: imagePos.x,
					y: imagePos.y,
					radius: 0,
					fill: drawConfig.fill,
					stroke: drawConfig.stroke,
					strokeWidth: drawConfig.strokeWidth,
					name: "mark",
					id: id,
				});
				break;
			case "line":
				shape = new Konva.Line({
					points: [imagePos.x, imagePos.y, imagePos.x, imagePos.y],
					stroke: drawConfig.stroke,
					strokeWidth: drawConfig.strokeWidth,
					name: "mark",
					id: id,
				});
				showLength.value = true;
				break;
		}

		if (shape) {
			drawLayer.value.add(shape);
			drawLayer.value.batchDraw();
		}
	};

	// 基本图形绘制过程
	const handleShapeDrawMove = (e: MouseEvent) => {
		if (!stage.value || !drawLayer.value) return;

		e.preventDefault();
		e.stopPropagation();

		const pos = stage.value.getPointerPosition();
		if (!pos) return;

		const imagePos = {
			x: (pos.x - position.value.x) / scale.value,
			y: (pos.y - position.value.y) / scale.value,
		};

		const shape = drawLayer.value.findOne(`#${markStartPosition.value.id}`);
		if (!shape) return;

		if (drawShapeType.value === "rect") {
			const rect = shape as Konva.Rect;
			const width = imagePos.x - markStartPosition.value.x;
			const height = imagePos.y - markStartPosition.value.y;
			rect.width(Math.abs(width));
			rect.height(Math.abs(height));
			rect.x(width < 0 ? imagePos.x : markStartPosition.value.x);
			rect.y(height < 0 ? imagePos.y : markStartPosition.value.y);
		} else if (drawShapeType.value === "circle") {
			const circle = shape as Konva.Circle;
			const dx = imagePos.x - markStartPosition.value.x;
			const dy = imagePos.y - markStartPosition.value.y;
			const radius = Math.sqrt(dx * dx + dy * dy);
			circle.radius(radius);
		} else if (drawShapeType.value === "line") {
			const line = shape as Konva.Line;
			const points = [
				markStartPosition.value.x,
				markStartPosition.value.y,
				imagePos.x,
				imagePos.y,
			];
			line.points(points);

			// 计算长度
			const dx = imagePos.x - markStartPosition.value.x;
			const dy = imagePos.y - markStartPosition.value.y;
			const length = Math.sqrt(dx * dx + dy * dy);
			currentLength.value = Math.round(length);

			// 更新长度显示位置
			const midX = (markStartPosition.value.x + imagePos.x) / 2;
			const midY = (markStartPosition.value.y + imagePos.y) / 2;
			const stagePos = stage.value!.getAbsolutePosition();
			lengthIndicatorStyle.value = {
				left: `${midX * scale.value + stagePos.x}px`,
				top: `${midY * scale.value + stagePos.y}px`,
			};
		}
		drawLayer.value.batchDraw();
	};

	// 基本图形绘制结束
	const handleShapeDrawEnd = (e: MouseEvent) => {
		if (!drawLayer.value) return;

		e.preventDefault();
		e.stopPropagation();

		document.removeEventListener("mousemove", handleShapeDrawMove);

		const shape = drawLayer.value.findOne(`#${markStartPosition.value.id}`);
		if (!shape) return;

		// 根据模式选择不同的缩放因子计算方式
		let maxZoomFactor;
		if (props.isTile) {
			// 切图模式使用原有的缩放因子计算方式
			const currentZoom = Math.floor(scale.value);
			maxZoomFactor = Math.pow(
				2,
				imageConfig.value.maxZoom - currentZoom
			);
		} else {
			// 不切图模式使用实际图片尺寸
			maxZoomFactor = 1;
		}

		if (drawShapeType.value === "rect") {
			const rect = shape as Konva.Rect;
			// 检查矩形是否太小
			if (rect.width() < 1 || rect.height() < 1) {
				rect.destroy();
				drawLayer.value.batchDraw();
				return;
			}
			marks.value.push({
				id: rect.id(),
				type: "rect",
				x: rect.x() * maxZoomFactor,
				y: rect.y() * maxZoomFactor,
				w: rect.width() * maxZoomFactor,
				h: rect.height() * maxZoomFactor,
				z: props.isTile ? imageConfig.value.maxZoom : 1,
			});
		} else if (drawShapeType.value === "circle") {
			const circle = shape as Konva.Circle;
			// 检查圆形是否太小
			if (circle.radius() < 1) {
				circle.destroy();
				drawLayer.value.batchDraw();
				return;
			}
			marks.value.push({
				id: circle.id(),
				type: "circle",
				x: circle.x() * maxZoomFactor,
				y: circle.y() * maxZoomFactor,
				radius: circle.radius() * maxZoomFactor,
				z: props.isTile ? imageConfig.value.maxZoom : 1,
			});
		} else if (drawShapeType.value === "line") {
			const line = shape as Konva.Line;
			const points = line.points();

			// 检查直线是否太短
			const dx = points[2] - points[0];
			const dy = points[3] - points[1];
			const length = Math.sqrt(dx * dx + dy * dy);

			if (length < 1) {
				line.destroy();
				drawLayer.value.batchDraw();
				showLength.value = false;
				return;
			}

			marks.value.push({
				id: line.id(),
				type: "line",
				x1: points[0] * maxZoomFactor,
				y1: points[1] * maxZoomFactor,
				x2: points[2] * maxZoomFactor,
				y2: points[3] * maxZoomFactor,
				z: props.isTile ? imageConfig.value.maxZoom : 1,
			});

			showLength.value = false;
		}

		renderMarks();
		isDrawMode.value = false;
		currentTileTool.value = null;
	};

	// 多边形绘制
	const handlePolygonDraw = (imagePos: { x: number; y: number }) => {
		if (!drawLayer.value) return;

		polygonPoints.value.push(imagePos.x, imagePos.y);

		if (!currentPolygon.value) {
			currentPolygon.value = new Konva.Line({
				points: polygonPoints.value,
				fill: drawConfig.fill,
				stroke: drawConfig.stroke,
				strokeWidth: drawConfig.strokeWidth,
				closed: true,
				name: "mark",
				id: Date.now().toString(),
			});
			drawLayer.value.add(currentPolygon.value as Konva.Line);
		} else {
			currentPolygon.value.points(polygonPoints.value);
		}
		drawLayer.value.batchDraw();
	};

	// 多边形绘制过程
	const handlePolygonDrawMove = throttle(
		(e: MouseEvent) => {
			if (!stage.value) return;

			stage.value.setPointersPositions(e);

			if (
				isDrawMode.value &&
				drawShapeType.value === "polygon" &&
				currentPolygon.value
			) {
				const pos = stage.value.getPointerPosition();
				if (!pos) return;

				const imagePos = {
					x: (pos.x - position.value.x) / scale.value,
					y: (pos.y - position.value.y) / scale.value,
				};

				const points = [...polygonPoints.value];
				points.push(imagePos.x, imagePos.y);
				currentPolygon.value.points(points);
				drawLayer.value?.batchDraw();
			}

			// 坐标计算逻辑
			const pos = stage.value.getPointerPosition();
			if (!pos) return;

			const imagePos = {
				x: Math.round((pos.x - position.value.x) / scale.value),
				y: Math.round((pos.y - position.value.y) / scale.value),
			};

			// 更新坐标显示
			coordinates.value = imagePos;
			showCoordinates.value = true;

			// 计算灰度值
			if (props.isTile) {
				// 切图模式使用原有的灰度值计算方法
				calculateGrayValue(
					imagePos,
					stage,
					layer,
					scale,
					imageConfig.value,
					grayValue
				);
			} else if (fullImage.value) {
				// 不切图模式下，从完整图片中计算灰度值
				calculateGrayValueFromFullImage(
					imagePos,
					fullImage.value,
					grayValue
				);
			} else {
				grayValue.value = -1;
			}
		},
		16,
		{ leading: true, trailing: true }
	);

	// 从完整图片中计算灰度值
	const calculateGrayValueFromFullImage = (
		pos: { x: number; y: number },
		image: HTMLImageElement,
		grayValue: Ref<number>
	) => {
		// 确保坐标在图片范围内
		if (
			pos.x < 0 ||
			pos.y < 0 ||
			pos.x >= image.naturalWidth ||
			pos.y >= image.naturalHeight
		) {
			grayValue.value = -1;
			return;
		}

		// 创建临时canvas来获取像素数据
		const canvas = document.createElement("canvas");
		const context = canvas.getContext("2d", { willReadFrequently: true });
		if (!context) {
			grayValue.value = -1;
			return;
		}

		// 设置canvas大小为图片大小
		canvas.width = image.naturalWidth;
		canvas.height = image.naturalHeight;

		// 绘制图片到canvas
		context.drawImage(image, 0, 0);

		try {
			// 获取像素数据
			const imageData = context.getImageData(
				Math.floor(pos.x),
				Math.floor(pos.y),
				1,
				1
			).data;
			const r = imageData[0];
			const g = imageData[1];
			const b = imageData[2];

			// 使用心理学公式计算灰度
			const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b);
			grayValue.value = gray;
		} catch (e) {
			console.error("Failed to get pixel data:", e);
			grayValue.value = -1;
		}
	};

	// 多边形绘制结束
	const handlePolygonDrawEnd = () => {
		if (!currentPolygon.value || polygonPoints.value.length < 6) {
			currentPolygon.value?.destroy();
			currentPolygon.value = null;
			polygonPoints.value = [];
			return;
		}

		// 根据模式选择不同的缩放因子计算方式
		let maxZoomFactor;
		if (props.isTile) {
			// 切图模式使用原有的缩放因子计算方式
			const currentZoom = Math.floor(scale.value);
			maxZoomFactor = Math.pow(
				2,
				imageConfig.value.maxZoom - currentZoom
			);
		} else {
			// 不切图模式使用实际图片尺寸
			maxZoomFactor = 1;
		}

		const pointObjects: { x: number; y: number }[] = [];
		for (let i = 0; i < polygonPoints.value.length; i += 2) {
			pointObjects.push({
				x: polygonPoints.value[i] * maxZoomFactor,
				y: polygonPoints.value[i + 1] * maxZoomFactor,
			});
		}

		marks.value.push({
			id: currentPolygon.value.attrs.id,
			type: "polygon",
			points: pointObjects,
			z: props.isTile ? imageConfig.value.maxZoom : 1,
		});

		// 重置状态
		currentPolygon.value = null;
		polygonPoints.value = [];
		renderMarks();
		isDrawMode.value = false;
		currentTileTool.value = null;
	};

	// 重置选中标记
	const resetMarkSelected = () => {
		if (!drawLayer.value) return;
		// 取消所有标记的选中状态
		const transformers = drawLayer.value.find("Transformer");
		transformers.forEach((tr) => tr.destroy());
		drawLayer.value.find(".mark").forEach((mark) => {
			mark.draggable(false);
		});
		const anchors = drawLayer.value.find(".polygon-anchor");
		anchors.forEach((anchor) => anchor.destroy());

		if (
			stage.value &&
			stage.value.container() &&
			stage.value.container().dataset.editingPolygon === "true"
		) {
			delete stage.value.container().dataset.editingPolygon;
		}
		drawLayer.value.batchDraw();
		showLength.value = false; // 隐藏长度指示器
	};

	// 删除选中标记
	const handleKeyDown = (e: KeyboardEvent) => {
		if (e.key === "Backspace") {
			// 瓦片模式下的删除处理
			if (screenMode.value === "tiles" && drawLayer.value) {
				// 检查是否处于多边形编辑模式
				if (
					stage.value &&
					stage.value.container().dataset.editingPolygon === "true"
				) {
					// 多边形编辑模式下的删除
					const polyAnchors = drawLayer.value.find(".polygon-anchor");
					if (polyAnchors.length > 0) {
						// 从第一个锚点获取多边形ID（格式为：polygon-id-anchor-0）
						const anchorId = polyAnchors[0].attrs.id;
						const polyId = anchorId.split("-anchor-")[0];

						// 查找并删除多边形
						const idx = marks.value.findIndex(
							(mark) => mark.id === polyId
						);
						if (idx > -1) {
							marks.value.splice(idx, 1);
						}

						// 清除所有锚点
						polyAnchors.forEach((anchor) => anchor.destroy());

						// 清除编辑状态
						if (stage.value) {
							delete stage.value.container().dataset
								.editingPolygon;
						}

						// 重新渲染
						copiedMarks.value = [];
						isCopyMode.value = false;
						renderMarks();
					}
				} else {
					// 普通模式下的删除处理
					const transformers = drawLayer.value.find("Transformer");
					if (transformers.length > 0) {
						const selectedNode = (
							transformers[0] as Konva.Transformer
						).nodes()[0];
						if (selectedNode) {
							const idx = marks.value.findIndex(
								(mark) => mark.id === selectedNode.attrs.id
							);
							if (idx > -1) {
								marks.value.splice(idx, 1);
							}
							copiedMarks.value = [];
							isCopyMode.value = false;
							renderMarks();
						}
					}
				}
			}
		}
	};

	// 渲染标记
	const renderMarks = () => {
		if (!drawLayer.value) return;

		// 强制确保drawLayer可见性
		drawLayer.value.visible(true);

		// 清除所有标记和变换器
		drawLayer.value.find(".mark").forEach((mark) => mark.destroy());
		drawLayer.value.find("Transformer").forEach((tr) => tr.destroy());

		const currentZoom = Math.floor(scale.value);

		// 获取视口范围
		const stageRef = { value: stage.value } as Ref<Konva.Stage | null>;
		const viewportBounds = getViewportBounds(stageRef, container, scale);

		// 根据模式选择不同的视口检查逻辑
		let visibleMarks;
		if (props.isTile) {
			// 切图模式使用原有的视口检查逻辑
			visibleMarks = marks.value.filter((markData) => {
				return isMarkInViewport(
					markData,
					viewportBounds,
					currentZoom,
					scale,
					imageConfig.value
				);
			});
		} else {
			// 不切图模式下，简化视口检查，直接使用所有标记
			visibleMarks = marks.value;
		}

		// 如果标记太多且处于简化模式，则进一步限制渲染的标记数量
		let marksToRender = visibleMarks;
		if (simplifiedMode.value && visibleMarks.length > 100) {
			const samplingRate = Math.max(
				1,
				Math.floor(visibleMarks.length / 100)
			);
			marksToRender = visibleMarks.filter(
				(_, index) => index % samplingRate === 0
			);
		}

		// 检查是否有标记需要渲染
		if (
			marksToRender.length === 0 &&
			!simplifiedMode.value &&
			marks.value.length > 0
		) {
			// 如果视口内没有标记但全局有标记，尝试渲染一些全局标记（最多100个）
			marksToRender = marks.value.slice(0, 100);
		}

		// 根据模式选择不同的缩放因子计算方式
		let zoomFactor;
		if (props.isTile) {
			// 切图模式使用原有的缩放因子计算方式
			zoomFactor = Math.pow(2, currentZoom - imageConfig.value.maxZoom);
		} else {
			// 不切图模式使用固定值1，因为标记坐标已经是基于原始图片尺寸的
			zoomFactor = 1;
		}

		// 只渲染处理后的标记
		marksToRender.forEach((markData) => {
			let mark: Konva.Shape | null = null;

			const customStyle = (markData as any).style || {};
			const fillStyle = customStyle.fill || drawConfig.fill;
			const strokeStyle = customStyle.stroke || drawConfig.stroke;
			const strokeWidth = drawConfig.strokeWidth;

			try {
				switch (markData.type) {
					case "rect": {
						mark = new Konva.Rect({
							x: markData.x * zoomFactor,
							y: markData.y * zoomFactor,
							width: markData.w * zoomFactor,
							height: markData.h * zoomFactor,
							fill: fillStyle,
							stroke: strokeStyle,
							strokeWidth: strokeWidth,
							name: "mark",
							id: markData.id,
							draggable: false, // 默认不可拖动
						});
						break;
					}
					case "circle": {
						mark = new Konva.Circle({
							x: markData.x * zoomFactor,
							y: markData.y * zoomFactor,
							radius: markData.radius * zoomFactor,
							fill: fillStyle,
							stroke: strokeStyle,
							strokeWidth: strokeWidth,
							name: "mark",
							id: markData.id,
							draggable: false, // 默认不可拖动
						});
						break;
					}
					case "polygon": {
						const flatPoints = markData.points.reduce(
							(acc: number[], point) => {
								acc.push(
									point.x * zoomFactor,
									point.y * zoomFactor
								);
								return acc;
							},
							[]
						);
						mark = new Konva.Line({
							points: flatPoints,
							fill: fillStyle,
							stroke: strokeStyle,
							strokeWidth: strokeWidth,
							closed: true,
							name: "mark",
							id: markData.id,
							draggable: false, // 默认不可拖动
						});
						break;
					}
					case "line": {
						mark = new Konva.Line({
							points: [
								markData.x1 * zoomFactor,
								markData.y1 * zoomFactor,
								markData.x2 * zoomFactor,
								markData.y2 * zoomFactor,
							],
							stroke: strokeStyle,
							strokeWidth: strokeWidth,
							name: "mark",
							id: markData.id,
							draggable: false, // 默认不可拖动
						});

						mark.on("click", () => {
							if (!stage.value) return;
							const points = (mark as Konva.Line).points();

							// 计算长度
							const dx = points[2] - points[0];
							const dy = points[3] - points[1];
							const length = Math.sqrt(dx * dx + dy * dy);
							currentLength.value = Math.round(length);

							// 计算中点位置
							const midX = (points[0] + points[2]) / 2;
							const midY = (points[1] + points[3]) / 2;
							const stagePos = stage.value.getAbsolutePosition();

							// 显示长度指示器
							showLength.value = true;
							lengthIndicatorStyle.value = {
								left: `${midX * scale.value + stagePos.x}px`,
								top: `${midY * scale.value + stagePos.y}px`,
							};
						});

						mark.on("transform dragmove", () => {
							if (!stage.value) return;
							const points = (mark as Konva.Line).points();

							// 计算长度
							const dx = points[2] - points[0];
							const dy = points[3] - points[1];
							const length = Math.sqrt(dx * dx + dy * dy);
							currentLength.value = Math.round(length);

							// 计算中点位置
							const midX = (points[0] + points[2]) / 2;
							const midY = (points[1] + points[3]) / 2;
							const stagePos = stage.value.getAbsolutePosition();

							// 显示长度指示器
							showLength.value = true;
							lengthIndicatorStyle.value = {
								left: `${midX * scale.value + stagePos.x}px`,
								top: `${midY * scale.value + stagePos.y}px`,
							};
						});
						break;
					}
				}

				if (mark) {
					drawLayer.value?.add(mark);

					// 更新标记位置和大小
					mark.on("transform dragend transformend", () => {
						const idx = marks.value.findIndex(
							(m) => m.id === mark!.attrs.id
						);
						if (idx === -1) return;

						// 根据模式选择不同的缩放因子计算方式
						let maxZoomFactor;
						if (props.isTile) {
							// 切图模式使用原有的缩放因子计算方式
							maxZoomFactor = Math.pow(
								2,
								imageConfig.value.maxZoom - currentZoom
							);
						} else {
							// 不切图模式使用实际图片尺寸
							maxZoomFactor = 1;
						}

						const oldMark = marks.value[idx];

						switch (oldMark.type) {
							case "rect": {
								const rect = mark as Konva.Rect;
								marks.value[idx] = {
									...oldMark,
									x: rect.x() * maxZoomFactor,
									y: rect.y() * maxZoomFactor,
									w:
										rect.width() *
										rect.scaleX() *
										maxZoomFactor,
									h:
										rect.height() *
										rect.scaleY() *
										maxZoomFactor,
								};
								// 重置缩放比并更新尺寸
								const width = rect.width() * rect.scaleX();
								const height = rect.height() * rect.scaleY();
								rect.width(width);
								rect.height(height);
								rect.scaleX(1);
								rect.scaleY(1);
								break;
							}
							case "circle": {
								const circle = mark as Konva.Circle;
								marks.value[idx] = {
									...oldMark,
									x: circle.x() * maxZoomFactor,
									y: circle.y() * maxZoomFactor,
									radius:
										circle.radius() *
										circle.scaleX() *
										maxZoomFactor,
								};
								// 重置缩放比并更新半径
								const radius =
									circle.radius() * circle.scaleX();
								circle.radius(radius);
								circle.scaleX(1);
								circle.scaleY(1);
								break;
							}
							case "polygon": {
								const polygon = mark as Konva.Line;
								const points = polygon.points();

								// 获取当前变换信息
								const transform = polygon.getTransform();
								const scaleX = polygon.scaleX();
								const scaleY = polygon.scaleY();
								const rotation = polygon.rotation();
								const x = polygon.x();
								const y = polygon.y();

								// 应用变换到每个点
								marks.value[idx] = {
									...oldMark,
									points: chunk(points, 2).map(([px, py]) => {
										// 创建点对象
										const point = { x: px, y: py };

										// 应用缩放
										point.x *= scaleX;
										point.y *= scaleY;

										// 应用旋转
										if (rotation !== 0) {
											const rad =
												(rotation * Math.PI) / 180;
											const cos = Math.cos(rad);
											const sin = Math.sin(rad);
											const nx =
												point.x * cos - point.y * sin;
											const ny =
												point.x * sin + point.y * cos;
											point.x = nx;
											point.y = ny;
										}

										// 应用平移
										point.x += x;
										point.y += y;

										// 应用最大缩放因子
										return {
											x: point.x * maxZoomFactor,
											y: point.y * maxZoomFactor,
										};
									}),
								};

								// 重置变换并更新点位置
								polygon.setAttrs({
									points: chunk(points, 2)
										.map(([px, py]) => {
											const point = { x: px, y: py };
											point.x *= scaleX;
											point.y *= scaleY;
											if (rotation !== 0) {
												const rad =
													(rotation * Math.PI) / 180;
												const cos = Math.cos(rad);
												const sin = Math.sin(rad);
												const nx =
													point.x * cos -
													point.y * sin;
												const ny =
													point.x * sin +
													point.y * cos;
												point.x = nx;
												point.y = ny;
											}
											point.x += x;
											point.y += y;
											return [point.x, point.y];
										})
										.flat(),
									scaleX: 1,
									scaleY: 1,
									rotation: 0,
									x: 0,
									y: 0,
								});
								break;
							}
							case "line": {
								const line = mark as Konva.Line;
								const points = line.points();

								// 获取变换信息
								const transform = line.getTransform();

								// 应用变换矩阵到每个点
								const startPoint = transform.point({
									x: points[0],
									y: points[1],
								});
								const endPoint = transform.point({
									x: points[2],
									y: points[3],
								});

								// 更新数据
								marks.value[idx] = {
									...oldMark,
									x1: startPoint.x * maxZoomFactor,
									y1: startPoint.y * maxZoomFactor,
									x2: endPoint.x * maxZoomFactor,
									y2: endPoint.y * maxZoomFactor,
								};

								// 重置变换并更新点位置
								line.setAttrs({
									points: [
										startPoint.x,
										startPoint.y,
										endPoint.x,
										endPoint.y,
									],
									scaleX: 1,
									scaleY: 1,
									rotation: 0,
									x: 0,
									y: 0,
								});
								break;
							}
						}

						drawLayer.value?.batchDraw();
					});
				}
			} catch (error) {
				console.error("瓦片模式：标记渲染错误", error, markData);
			}
		});

		// 优化绘制方式，使用一次性批量绘制
		requestAnimationFrame(() => {
			drawLayer.value?.batchDraw();
		});

		// 绑定舞台点击事件
		if (stage.value) {
			// 移除之前的点击事件处理器以避免重复绑定
			stage.value.off("click");
			stage.value.on("click", (e) => {
				if (!drawLayer.value || isDrawMode.value) return;

				const clickedOnMark = e.target.hasName("mark");

				// 如果没有点击在标记上，重置选中状态
				if (!clickedOnMark) {
					resetMarkSelected();
					return;
				}

				// 获取原始标记数据
				const markId = e.target.attrs.id;
				const markData = marks.value.find((mark) => mark.id === markId);

				// 如果找到标记数据
				if (markData) {
					// 检查标记是否已经在copiedMarks中
					const existingIndex = copiedMarks.value.findIndex(
						(mark) => mark.id === markId
					);

					if (existingIndex === -1) {
						// 添加到copiedMarks
						const copiedMark = JSON.parse(JSON.stringify(markData));
						copiedMarks.value.push(copiedMark);
					} else {
						// 从copiedMarks中移除
						copiedMarks.value.splice(existingIndex, 1);
					}
				}

				// 重置之前的选中状态
				resetMarkSelected();

				// 检查是否是多边形
				if (
					e.target instanceof Konva.Line &&
					marks.value.find((m) => m.id === e.target.attrs.id)
						?.type === "polygon"
				) {
					// 多边形使用顶点变换器
					const polygon = e.target;
					const polygonPoints = polygon.points();
					const markIndex = marks.value.findIndex(
						(m) => m.id === polygon.attrs.id
					);

					isDragging.value = false;

					// 标记编辑多边形状态
					if (stage.value && stage.value.container()) {
						stage.value.container().dataset.editingPolygon = "true";
					}

					// 为每个顶点创建一个初始锚点
					for (let i = 0; i < polygonPoints.length; i += 2) {
						const isFirstPoint = i === 0;
						const anchor = new Konva.Circle({
							x: polygonPoints[i],
							y: polygonPoints[i + 1],
							radius: 5 / scale.value,
							fill: isFirstPoint ? "red" : "white",
							stroke: "orange",
							strokeWidth: 2 / scale.value,
							draggable: true,
							name: "polygon-anchor",
							id: `${polygon.attrs.id}-anchor-${i / 2}`,
						});

						// 锚点拖拽事件
						anchor.on("dragmove", () => {
							// 更新多边形点
							polygonPoints[i] = anchor.x();
							polygonPoints[i + 1] = anchor.y();
							polygon.points(polygonPoints);
							drawLayer.value?.batchDraw();
						});

						// 锚点拖拽结束事件 - 更新数据
						anchor.on("dragend", () => {
							if (markIndex !== -1) {
								// 根据模式选择不同的缩放因子计算方式
								let maxZoomFactor;
								if (props.isTile) {
									// 切图模式使用原有的缩放因子计算方式
									const currentZoom = Math.floor(scale.value);
									maxZoomFactor = Math.pow(
										2,
										imageConfig.value.maxZoom - currentZoom
									);
								} else {
									// 不切图模式使用实际图片尺寸
									maxZoomFactor = 1;
								}

								// 更新mark数据 - 构建新的points数组
								const newPoints: {
									x: number;
									y: number;
								}[] = [];
								for (
									let j = 0;
									j < polygonPoints.length;
									j += 2
								) {
									newPoints.push({
										x: polygonPoints[j] * maxZoomFactor,
										y: polygonPoints[j + 1] * maxZoomFactor,
									});
								}

								// 确保marks中的元素类型正确
								const updatedMark = marks.value[
									markIndex
								] as any;
								marks.value[markIndex] = {
									...updatedMark,
									points: newPoints,
								};
							}
						});

						drawLayer.value?.add(anchor);
					}

					drawLayer.value?.batchDraw();
				} else {
					// 基本类型使用普通变换器
					const tr = new Konva.Transformer({
						nodes: [e.target],
						keepRatio: false,
						rotateEnabled: false,
						ignoreStroke: true,
					});
					e.target.draggable(true); // 设置选中的标记为可拖动

					drawLayer.value.add(tr);
					drawLayer.value.batchDraw();
				}
			});
		}
	};

	// 瓦片模式 优化缩放操作，减少不必要的标记重绘
	const handleWheel = throttle(
		(e: WheelEvent) => {
			e.preventDefault();
			if (!stage.value) return;

			// 鼠标坐标
			const pointer = stage.value.getPointerPosition();
			if (!pointer) return;

			const oldScale = scale.value;
			const step = props.isTile ? 0.25 : 0.1; // 不切图模式使用更小的步长
			const delta = e.deltaY > 0 ? -step : step;
			const newScale = Math.min(
				Math.max(
					props.isTile
						? Math.round((oldScale + delta) * 4) / 4
						: oldScale + delta,
					minScale.value // 最小值
				),
				maxScale.value // 使用计算的最大缩放比
			);
			// 如果缩放级别相同，则不进行处理
			if (newScale === oldScale) return;

			// 设置为简化模式并隐藏标记
			if (!simplifiedMode.value) {
				simplifiedMode.value = true;
				if (drawLayer.value) {
					drawLayer.value.visible(false);
				}
			}

			// 鼠标相对 stage 的坐标
			const mousePointTo = {
				x: (pointer.x - stage.value.x()) / oldScale,
				y: (pointer.y - stage.value.y()) / oldScale,
			};

			let newPos = { x: 0, y: 0 };

			if (props.isTile) {
				// 切图模式使用原有的缩放逻辑
				const zoomLevels = Array.from(
					{ length: imageConfig.value.maxZoom - 1 },
					(_, i) => i + 2
				);

				if (delta > 0) {
					// 放大
					newPos = {
						x: zoomLevels.includes(newScale)
							? pointer.x - mousePointTo.x * newScale * 2
							: pointer.x - mousePointTo.x * newScale,
						y: zoomLevels.includes(newScale)
							? pointer.y - mousePointTo.y * newScale * 2
							: pointer.y - mousePointTo.y * newScale,
					};
				} else {
					// 缩小
					newPos = {
						x: zoomLevels.includes(oldScale)
							? pointer.x - (mousePointTo.x * newScale) / 2
							: pointer.x - mousePointTo.x * newScale,
						y: zoomLevels.includes(oldScale)
							? pointer.y - (mousePointTo.y * newScale) / 2
							: pointer.y - mousePointTo.y * newScale,
					};
				}
			} else {
				// 不切图模式使用简化的缩放逻辑
				newPos = {
					x: pointer.x - mousePointTo.x * newScale,
					y: pointer.y - mousePointTo.y * newScale,
				};
			}

			requestAnimationFrame(() => {
				if (!stage.value) return;

				// 检查是否跨越了缩放临界点1
				const crossedThreshold =
					(oldScale >= 1 && newScale < 1) ||
					(oldScale < 1 && newScale >= 1);

				scale.value = newScale; // 更新缩放

				// 更新舞台
				stage.value.scale({ x: newScale, y: newScale });
				stage.value.position(newPos);
				stage.value.batchDraw();

				position.value = newPos; // 更新 position.value 以触发 visibleTiles 的重新计算

				// 如果跨越了缩放临界点1，需要特殊处理
				if (crossedThreshold && props.isTile) {
					if (newScale < 1) {
						// 从瓦片模式切换到低分辨率模式
						if (lowResImage.value) {
							renderLowResImage();
						} else {
							loadLowResImage();
						}
					} else {
						// 从低分辨率模式切换到瓦片模式
						if (layer.value) {
							const lowResImageNode =
								layer.value.findOne("#low-res-image");
							if (lowResImageNode) {
								lowResImageNode.destroy();
							}
							layer.value.batchDraw();
						}
					}
				}
			});
		},
		50,
		{ leading: true, trailing: true }
	);

	// 瓦片模式 缩放结束
	const handleWheelEnd = debounce(() => {
		if (drawLayer.value) {
			drawLayer.value.visible(true);
		}
		if (simplifiedMode.value) {
			simplifiedMode.value = false;
			requestAnimationFrame(() => {
				renderMarks();
			});
		} else {
			// 即使不是简化模式也检查是否需要重新渲染标记
			if (marks.value.length > 0 && !isDragging.value) {
				requestAnimationFrame(() => {
					renderMarks();
				});
			}
		}
	}, 300);

	// 瓦片模式 监听缩放事件，缩放结束
	watch(scale, () => {
		resetMarkSelected();
		handleWheelEnd();
	});

	// 瓦片模式 拖动相关
	const handleDragStart = (e: MouseEvent) => {
		// 瓦片模式的拖动逻辑
		if (!stage.value || !drawLayer.value) return;

		// 如果正在编辑多边形，则不允许进行拖动操作
		if (stage.value.container().dataset.editingPolygon === "true") {
			return;
		}

		// 右键点击处理
		if (e.button === 2) {
			if (drawShapeType.value === "polygon" && currentPolygon.value) {
				handlePolygonDrawEnd();
			}
			isDrawMode.value = false;
			currentTileTool.value = null;
			copiedMarks.value = [];
			isCopyMode.value = false;
			return;
		}

		// 获取鼠标点击位置的对象
		const pos = stage.value.getPointerPosition();
		if (!pos) return;

		// 检查点击是否在标记上
		const clickedTarget = stage.value.getIntersection(pos);
		const clickedOnMark = clickedTarget && clickedTarget.hasName("mark");

		// 如果点击在标记上，不执行拖拽移动
		if (clickedOnMark) {
			return;
		}

		// 在复制模式下，不在标记上点击时复制标记
		if (
			isCopyMode.value &&
			currentTileTool.value === "copy" &&
			copiedMarks.value.length > 0
		) {
			// 计算图像坐标系中的位置
			const imagePos = {
				x: (pos.x - position.value.x) / scale.value,
				y: (pos.y - position.value.y) / scale.value,
			};

			// 获取当前缩放级别
			const currentZoom = Math.floor(scale.value);
			const maxZoomFactor = Math.pow(
				2,
				imageConfig.value.maxZoom - currentZoom
			);

			// 复制每个标记并添加到marks
			copiedMarks.value.forEach((markToCopy) => {
				const newMark = { ...JSON.parse(JSON.stringify(markToCopy)) };
				// 生成新的ID
				newMark.id =
					Date.now() + "-" + Math.random().toString(36).substr(2, 9);

				// 根据不同类型的标记计算偏移
				switch (markToCopy.type) {
					case "rect": {
						// 矩形计算左上角点与鼠标位置的偏移
						const offsetX =
							imagePos.x - markToCopy.x / maxZoomFactor;
						const offsetY =
							imagePos.y - markToCopy.y / maxZoomFactor;
						newMark.x =
							(markToCopy.x / maxZoomFactor + offsetX) *
							maxZoomFactor;
						newMark.y =
							(markToCopy.y / maxZoomFactor + offsetY) *
							maxZoomFactor;
						break;
					}
					case "circle": {
						// 圆计算圆心与鼠标位置的偏移
						const offsetX =
							imagePos.x - markToCopy.x / maxZoomFactor;
						const offsetY =
							imagePos.y - markToCopy.y / maxZoomFactor;
						newMark.x =
							(markToCopy.x / maxZoomFactor + offsetX) *
							maxZoomFactor;
						newMark.y =
							(markToCopy.y / maxZoomFactor + offsetY) *
							maxZoomFactor;
						break;
					}
					case "polygon": {
						// 多边形计算第一个点与鼠标位置的偏移
						if (markToCopy.points && markToCopy.points.length > 0) {
							const firstPoint = markToCopy.points[0];
							const offsetX =
								imagePos.x - firstPoint.x / maxZoomFactor;
							const offsetY =
								imagePos.y - firstPoint.y / maxZoomFactor;

							// 更新所有点的位置
							newMark.points = markToCopy.points.map((point) => ({
								x:
									(point.x / maxZoomFactor + offsetX) *
									maxZoomFactor,
								y:
									(point.y / maxZoomFactor + offsetY) *
									maxZoomFactor,
							}));
						}
						break;
					}
					case "line": {
						// 直线计算第一个点与鼠标位置的偏移
						const offsetX =
							imagePos.x - markToCopy.x1 / maxZoomFactor;
						const offsetY =
							imagePos.y - markToCopy.y1 / maxZoomFactor;
						newMark.x1 =
							(markToCopy.x1 / maxZoomFactor + offsetX) *
							maxZoomFactor;
						newMark.y1 =
							(markToCopy.y1 / maxZoomFactor + offsetY) *
							maxZoomFactor;
						newMark.x2 =
							(markToCopy.x2 / maxZoomFactor + offsetX) *
							maxZoomFactor;
						newMark.y2 =
							(markToCopy.y2 / maxZoomFactor + offsetY) *
							maxZoomFactor;
						break;
					}
				}

				// 添加新标记到marks数组
				marks.value.push(newMark);
			});

			// 重新渲染标记
			renderMarks();
			isCopyMode.value = false;
			copiedMarks.value = [];
			currentTileTool.value = null;
			return;
		}

		const transformers = drawLayer.value.find("Transformer");
		if (transformers.length > 0) return;

		if (isDrawMode.value) {
			e.preventDefault(); // 阻止默认事件
			e.stopPropagation(); // 阻止事件冒泡

			const imagePos = {
				x: (pos.x - position.value.x) / scale.value,
				y: (pos.y - position.value.y) / scale.value,
			};

			// 禁用所有标记的拖动
			drawLayer.value.find(".mark").forEach((mark) => {
				mark.draggable(false);
			});

			if (drawShapeType.value === "polygon") {
				handlePolygonDraw(imagePos);
			} else {
				handleShapeDraw(imagePos, drawShapeType.value);
				// 在非多边形模式下，添加一次性的鼠标事件监听
				document.addEventListener("mousemove", handleShapeDrawMove);
				document.addEventListener("mouseup", handleShapeDrawEnd, {
					once: true,
				});
			}
			return; // 在绘制模式下直接返回，不执行拖动逻辑
		}

		// 非绘制模式下的拖动逻辑
		isDragging.value = true;
		dragStartPosition.value = {
			x: e.clientX,
			y: e.clientY,
		};
		dragLastPosition.value = { ...position.value };

		document.addEventListener("mousemove", handleDragMove);
		document.addEventListener("mouseup", handleDragEnd, { once: true }); // 使用 once 选项确保只触发一次
	};

	// 优化拖拽处理函数
	const handleDragMove = throttle(
		(e: MouseEvent) => {
			if (!isDragging.value || !stage.value) return;

			// 拖拽时如果标记层可见，则暂时隐藏以提高性能
			if (drawLayer.value && drawLayer.value.visible()) {
				simplifiedMode.value = true;
				drawLayer.value.visible(false);
			}

			const deltaX = e.clientX - dragStartPosition.value.x;
			const deltaY = e.clientY - dragStartPosition.value.y;

			position.value = {
				x: dragLastPosition.value.x + deltaX,
				y: dragLastPosition.value.y + deltaY,
			};

			// 使用requestAnimationFrame优化渲染
			requestAnimationFrame(() => {
				if (!stage.value) return;
				stage.value.position({
					x: position.value.x,
					y: position.value.y,
				});

				// 拖拽时设置为简化模式，提高性能
				if (!simplifiedMode.value) {
					simplifiedMode.value = true;
					// 拖拽时隐藏所有标记以提高性能
					if (drawLayer.value && drawLayer.value.visible()) {
						drawLayer.value.visible(false);
					}
				}

				stage.value.batchDraw();
			});
		},
		16,
		{ leading: true, trailing: true }
	);

	// 添加拖拽结束时恢复正常渲染模式
	const handleDragEnd = () => {
		isDragging.value = false;
		document.removeEventListener("mousemove", handleDragMove); // 确保移除移动事件监听

		// 拖拽结束后恢复标记显示和正常渲染
		if (drawLayer.value) {
			drawLayer.value.visible(true);
		}
		if (simplifiedMode.value) {
			simplifiedMode.value = false;
			// 使用延迟以确保界面响应先恢复
			setTimeout(() => {
				renderMarks(); // 重新渲染标记
			}, 50);
		} else {
			// 即使不是简化模式也尝试重新渲染标记，确保标记显示
			setTimeout(() => {
				renderMarks();
			}, 50);
		}
	};

	const resetFullImage = () => {
		fullImage.value = null;
		isFullImageLoading.value = false;
	};

	return {
		currentTileTool,
		tileTools,
		handleTileToolClick,
		tileCache,
		polygonPoints,
		currentPolygon,
		container,
		stageContainer,
		stage,
		layer,
		drawLayer,
		marks,
		scale,
		position,
		simplifiedMode,
		showLength,
		currentLength,
		lengthIndicatorStyle,
		showCoordinates,
		coordinates,
		grayValue,
		isDrawMode,
		isDragging,
		initStage,
		renderMarks,
		handleWheel,
		handleWheelEnd,
		handleDragStart,
		handleDragMove,
		handleDragEnd,
		handlePolygonDrawMove,
		handleKeyDown,
		resetFullImage,
	};
};
