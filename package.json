{"name": "semi", "private": true, "version": "1.0.0", "main": "index.js", "scripts": {"changeset": "changeset", "tag": "changeset version", "dev:wafer": "pnpm -F wafer-web dev", "build:wafer": "pnpm -F wafer-web build", "neu:wafer": "pnpm -F wafer-web neu", "dev:afm": "pnpm -F afm-web dev", "build:afm": "pnpm -F afm-web build", "neu:afm": "pnpm -F afm-web neu"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@changesets/cli": "^2.27.9", "@cw/components": "workspace:^", "@cw/logic": "workspace:^", "@deck.gl/core": "^9.0.38", "@deck.gl/mesh-layers": "^9.0.38", "@neutralinojs/lib": "^6.0.0", "@types/lodash": "^4.17.12", "dayjs": "^1.11.13", "echarts": "^5.6.0", "google-protobuf": "^3.21.4", "grpc-web": "^1.5.0", "less": "^4.2.0", "lodash": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.30.1", "plotly.js-dist": "^2.35.3", "react": "^18.3.1", "stats.js": "^0.17.0", "three": "^0.171.0", "vite-plugin-electron": "^0.29.0", "vite-plugin-pwa": "^0.21.1"}, "engines": {"node": ">=22", "pnpm": ">=9"}, "dependencies": {"@deck.gl/aggregation-layers": "9.0.38", "@deck.gl/layers": "9.0.38", "@element-plus/icons-vue": "^2.3.1", "@luma.gl/constants": "9.0.28", "@luma.gl/core": "9.0.28", "@luma.gl/engine": "9.0.28", "@turf/turf": "^7.1.0", "@vueuse/core": "^11.3.0", "@websee/core": "^4.0.2", "@websee/performance": "^4.0.2", "@websee/recordscreen": "^4.0.2", "axios": "^1.7.7", "d3-scale": "^4.0.2", "element-plus": "^2.9.3", "konva": "^9.3.16", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.1", "unplugin-icons": "^0.19.3", "v3-drag-zoom": "^1.1.20", "vue": "^3.5.10", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5"}}