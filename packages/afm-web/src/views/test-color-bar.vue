<template>
  <div class="test-color-bar-container">
    <h2>颜色条测试页面</h2>
    <div class="instructions">
      <h3>使用说明：</h3>
      <ul>
        <li>右侧有一个颜色条，显示从最小值颜色（底部）到最大值颜色（顶部）的渐变</li>
        <li>点击颜色条可以打开配置对话框</li>
        <li>在配置对话框中可以设置：最小值、最大值、最小值颜色、最大值颜色</li>
        <li>默认设置：最小值 0（蓝色），最大值 100（红色）</li>
      </ul>
    </div>
    <div class="test-content">
      <InspImage :src="testImageSrc" />
    </div>
  </div>
</template>

<script setup lang="ts">
import InspImage from '@/components/inspImage/index.vue'

// 测试图片（可以是任何图片URL或留空）
const testImageSrc = ref('')
</script>

<style lang="less" scoped>
.test-color-bar-container {
  width: 100%;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;

  h2 {
    margin-bottom: 20px;
    color: @cw-font-color;
  }

  .instructions {
    margin-bottom: 20px;
    padding: 15px;
    background-color: @cw-card-color;
    border: 1px solid @cw-border-color;
    border-radius: 8px;

    h3 {
      margin-bottom: 10px;
      color: @cw-font-color-title;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      color: @cw-font-color;

      li {
        margin-bottom: 5px;
        line-height: 1.5;
      }
    }
  }

  .test-content {
    width: 100%;
    height: calc(100% - 200px);
    border: 1px solid @cw-border-color;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
